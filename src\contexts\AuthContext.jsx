import React, { createContext, useContext, useReducer, useEffect } from 'react';
import { authAPI, tokenManager } from '../lib/apiClient';
import { handleError } from '../lib/utils';
import toast from 'react-hot-toast';

// Initial state
const initialState = {
    user: null,
    isAuthenticated: false,
    isLoading: true,
    error: null,
};

// Action types
const AUTH_ACTIONS = {
    SET_LOADING: 'SET_LOADING',
    SET_USER: 'SET_USER',
    SET_ERROR: 'SET_ERROR',
    LOGOUT: 'LOGOUT',
    CLEAR_ERROR: 'CLEAR_ERROR',
};

// Reducer function
const authReducer = (state, action) => {
    switch (action.type) {
        case AUTH_ACTIONS.SET_LOADING:
            return {
                ...state,
                isLoading: action.payload,
            };

        case AUTH_ACTIONS.SET_USER:
            return {
                ...state,
                user: action.payload,
                isAuthenticated: !!action.payload,
                isLoading: false,
                error: null,
            };

        case AUTH_ACTIONS.SET_ERROR:
            return {
                ...state,
                error: action.payload,
                isLoading: false,
            };

        case AUTH_ACTIONS.LOGOUT:
            return {
                ...state,
                user: null,
                isAuthenticated: false,
                isLoading: false,
                error: null,
            };

        case AUTH_ACTIONS.CLEAR_ERROR:
            return {
                ...state,
                error: null,
            };

        default:
            return state;
    }
};

// Create context
const AuthContext = createContext(null);

// AuthProvider component
export const AuthProvider = ({ children }) => {
    const [state, dispatch] = useReducer(authReducer, initialState);

    // Initialize authentication state
    useEffect(() => {
        const initializeAuth = async () => {
            try {
                const token = tokenManager.getToken();
                const userData = tokenManager.getUserData();

                if (token && userData) {
                    // Verify token is still valid by fetching user profile
                    try {
                        const response = await authAPI.getProfile();
                        dispatch({ type: AUTH_ACTIONS.SET_USER, payload: response.data.user });
                    } catch (error) {
                        // Token is invalid, clear storage
                        tokenManager.clearAll();
                        dispatch({ type: AUTH_ACTIONS.LOGOUT });
                    }
                } else {
                    dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: false });
                }
            } catch (error) {
                console.error('Auth initialization error:', error);
                dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: false });
            }
        };

        initializeAuth();
    }, []);

    // Login function
    const login = async (credentials) => {
        try {
            dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: true });
            dispatch({ type: AUTH_ACTIONS.CLEAR_ERROR });

            const response = await authAPI.login(credentials);
            const { user, access_token, refresh_token } = response.data;

            // Store tokens and user data
            tokenManager.setToken(access_token);
            if (refresh_token) {
                tokenManager.setRefreshToken(refresh_token);
            }
            tokenManager.setUserData(user);

            dispatch({ type: AUTH_ACTIONS.SET_USER, payload: user });
            toast.success(`Welcome back, ${user.name}!`);

            return { success: true, user };
        } catch (error) {
            const errorInfo = handleError(error, 'login');
            dispatch({ type: AUTH_ACTIONS.SET_ERROR, payload: errorInfo.message });
            toast.error(errorInfo.message);
            return { success: false, error: errorInfo.message };
        }
    };

    // Register function
    const register = async (userData) => {
        try {
            dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: true });
            dispatch({ type: AUTH_ACTIONS.CLEAR_ERROR });

            const response = await authAPI.register(userData);
            const { user, access_token, refresh_token } = response.data;

            // Store tokens and user data
            tokenManager.setToken(access_token);
            if (refresh_token) {
                tokenManager.setRefreshToken(refresh_token);
            }
            tokenManager.setUserData(user);

            dispatch({ type: AUTH_ACTIONS.SET_USER, payload: user });
            toast.success(`Welcome, ${user.name}! Your account has been created.`);

            return { success: true, user };
        } catch (error) {
            const errorInfo = handleError(error, 'register');
            dispatch({ type: AUTH_ACTIONS.SET_ERROR, payload: errorInfo.message });
            toast.error(errorInfo.message);
            return { success: false, error: errorInfo.message };
        }
    };

    // Logout function
    const logout = async () => {
        try {
            dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: true });

            // Call logout API to invalidate token on server
            try {
                await authAPI.logout();
            } catch (error) {
                // Continue with logout even if API call fails
                console.warn('Logout API call failed:', error);
            }

            // Clear local storage
            tokenManager.clearAll();
            dispatch({ type: AUTH_ACTIONS.LOGOUT });
            toast.success('You have been logged out successfully.');

            return { success: true };
        } catch (error) {
            const errorInfo = handleError(error, 'logout');
            // Still clear local storage even if API call fails
            tokenManager.clearAll();
            dispatch({ type: AUTH_ACTIONS.LOGOUT });
            return { success: false, error: errorInfo.message };
        }
    };

    // Forgot password function
    const forgotPassword = async (email) => {
        try {
            dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: true });
            dispatch({ type: AUTH_ACTIONS.CLEAR_ERROR });

            await authAPI.forgotPassword(email);
            toast.success('Password reset instructions have been sent to your email.');

            return { success: true };
        } catch (error) {
            const errorInfo = handleError(error, 'forgot password');
            dispatch({ type: AUTH_ACTIONS.SET_ERROR, payload: errorInfo.message });
            toast.error(errorInfo.message);
            return { success: false, error: errorInfo.message };
        } finally {
            dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: false });
        }
    };

    // Reset password function
    const resetPassword = async (token, password, passwordConfirmation) => {
        try {
            dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: true });
            dispatch({ type: AUTH_ACTIONS.CLEAR_ERROR });

            await authAPI.resetPassword(token, password, passwordConfirmation);
            toast.success('Your password has been reset successfully. Please login with your new password.');

            return { success: true };
        } catch (error) {
            const errorInfo = handleError(error, 'reset password');
            dispatch({ type: AUTH_ACTIONS.SET_ERROR, payload: errorInfo.message });
            toast.error(errorInfo.message);
            return { success: false, error: errorInfo.message };
        } finally {
            dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: false });
        }
    };

    // Update profile function
    const updateProfile = async (userData) => {
        try {
            dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: true });
            dispatch({ type: AUTH_ACTIONS.CLEAR_ERROR });

            const response = await authAPI.updateProfile(userData);
            const updatedUser = response.data.user;

            // Update stored user data
            tokenManager.setUserData(updatedUser);
            dispatch({ type: AUTH_ACTIONS.SET_USER, payload: updatedUser });
            toast.success('Profile updated successfully.');

            return { success: true, user: updatedUser };
        } catch (error) {
            const errorInfo = handleError(error, 'update profile');
            dispatch({ type: AUTH_ACTIONS.SET_ERROR, payload: errorInfo.message });
            toast.error(errorInfo.message);
            return { success: false, error: errorInfo.message };
        }
    };

    // Clear error function
    const clearError = () => {
        dispatch({ type: AUTH_ACTIONS.CLEAR_ERROR });
    };

    // Context value
    const value = {
        // State
        user: state.user,
        isAuthenticated: state.isAuthenticated,
        isLoading: state.isLoading,
        error: state.error,

        // Actions
        login,
        register,
        logout,
        forgotPassword,
        resetPassword,
        updateProfile,
        clearError,
    };

    return (
        <AuthContext.Provider value={value}>
            {children}
        </AuthContext.Provider>
    );
};

// Custom hook to use auth context
export const useAuth = () => {
    const context = useContext(AuthContext);

    if (!context) {
        throw new Error('useAuth must be used within an AuthProvider');
    }

    return context;
};



export default AuthContext;
