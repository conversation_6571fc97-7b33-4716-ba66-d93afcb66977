import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';

export const useNavigationHandler = () => {
    const navigate = useNavigate();

    useEffect(() => {
        const handleAuthLogout = () => {
            navigate('/login', { replace: true });
        };

        const handleAuthRedirect = (event) => {
            const { path, replace = true } = event.detail;
            navigate(path, { replace });
        };

        window.addEventListener('auth:logout', handleAuthLogout);
        window.addEventListener('auth:redirect', handleAuthRedirect);

        return () => {
            window.removeEventListener('auth:logout', handleAuthLogout);
            window.removeEventListener('auth:redirect', handleAuthRedirect);
        };
    }, [navigate]);
};
