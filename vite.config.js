// import { defineConfig } from 'vite'
// import react from '@vitejs/plugin-react'
// import tailwindcss from '@tailwindcss/vite'

// // https://vite.dev/config/
// export default defineConfig({
//     plugins: [
//         react(),
//         tailwindcss(),
//     ],
// })

import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import tailwindcss from '@tailwindcss/vite'; // Keep this for Tailwind CSS v4 plugin
import path from "path"; // <--- Add this import!

// https://vitejs.dev/config/
export default defineConfig({
    plugins: [
        react(),
        tailwindcss(), // Keep this for Tailwind CSS v4 plugin
    ],
    resolve: { // <--- Add this block!
        alias: {
            "@": path.resolve(__dirname, "./src"),
        },
    },
});